using OdmoriBa.Application.Features.Trips.Models;
using OdmoriBa.Core.Domains.Trips.Entities;

namespace OdmoriBa.Application.Features.Dashboard.Queries;

public sealed record GetDraftTripsQuery(int Take = 10) : IQuery<Result<ListResultDto<TripListItemDto>>>;

internal sealed class GetDraftTripsQueryHandler(IAppDbContext dbContext)
    : IQueryHandler<GetDraftTripsQuery, Result<ListResultDto<TripListItemDto>>>
{
    public async ValueTask<Result<ListResultDto<TripListItemDto>>> Handle(GetDraftTripsQuery request,
        CancellationToken cancellationToken)
    {
        var draftTrips = await dbContext.Trips
            .Include(t => t.TripDestinations)
            .ThenInclude(td => td.Destination)
            .Where(t => t.Status == TripStatus.Draft)
            .OrderByDescending(t => t.CreatedAt)
            .Take(request.Take)
            .Select(t => new TripListItemDto
            {
                Id = t.Id,
                Title = t.Title,
                Type = t.Type,
                Status = t.Status,
                StartDate = t.StartDate,
                EndDate = t.EndDate,
                TransportationType = t.TransportationType,
                Destinations = t.TripDestinations.Select(td => td.Destination!.Name).ToList(),
                NumberOfTravelers = 0,
                NumberOfTravelerRequests = 0
            })
            .ToListAsync(cancellationToken);

        return new ListResultDto<TripListItemDto>(draftTrips);
    }
}
