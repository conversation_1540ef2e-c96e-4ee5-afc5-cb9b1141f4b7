using OdmoriBa.Application.Features.Dashboard.Models;
using OdmoriBa.Core.Domains.Travelers.Entities;
using OdmoriBa.Core.Domains.Trips.Entities;

namespace OdmoriBa.Application.Features.Dashboard.Queries;

public sealed record GetDashboardStatsQuery : IQuery<Result<DashboardStatsDto>>;

internal sealed class GetDashboardStatsQueryHandler(IAppDbContext dbContext)
    : IQueryHandler<GetDashboardStatsQuery, Result<DashboardStatsDto>>
{
    public async ValueTask<Result<DashboardStatsDto>> Handle(GetDashboardStatsQuery request,
        CancellationToken cancellationToken)
    {
        var currentMonth = DateOnly.FromDateTime(DateTime.Now);
        var startOfMonth = new DateOnly(currentMonth.Year, currentMonth.Month, 1);
        var endOfMonth = startOfMonth.AddMonths(1).AddDays(-1);

        // Get trip statistics
        var tripStats = await dbContext.Trips
            .GroupBy(t => 1)
            .Select(g => new
            {
                TotalPublished = g.Count(t => t.Status == TripStatus.Published),
                TotalDraft = g.Count(t => t.Status == TripStatus.Draft),
                TripsThisMonth = g.Count(t => t.StartDate >= startOfMonth && t.StartDate <= endOfMonth)
            })
            .FirstOrDefaultAsync(cancellationToken);

        // Get traveler statistics
        var travelerStats = await dbContext.Travelers
            .Where(t => !t.IsDeleted)
            .GroupBy(t => 1)
            .Select(g => new
            {
                TotalPending = g.Count(t => t.Status == TravelerStatus.Draft || t.Status == TravelerStatus.Requested),
                TotalConfirmed = g.Count(t => t.Status == TravelerStatus.Confirmed),
                TotalRequested = g.Count(t => t.Status == TravelerStatus.Requested),
                TravelersThisMonth = g.Count(t => t.CreatedAt >= startOfMonth.ToDateTime(TimeOnly.MinValue) && 
                                                 t.CreatedAt <= endOfMonth.ToDateTime(TimeOnly.MaxValue))
            })
            .FirstOrDefaultAsync(cancellationToken);

        // Get financial statistics - calculate revenue from payments
        var totalRevenue = await dbContext.TravelParties
            .Where(tp => !tp.IsDeleted && tp.Travelers.Any(t => !t.IsDeleted))
            .SelectMany(tp => tp.Payments)
            .Where(p => !p.IsDeleted)
            .SumAsync(p => p.Amount, cancellationToken);

        // Calculate pending payments by getting total price minus total paid for each travel party
        var travelPartiesWithFinancials = await dbContext.TravelParties
            .Where(tp => !tp.IsDeleted && tp.Travelers.Any(t => !t.IsDeleted))
            .Select(tp => new
            {
                TotalPrice = tp.Travelers
                    .Where(t => !t.IsDeleted)
                    .Sum(t => t.Price + t.InsurancePrice + t.TaxPrice - t.Discount),
                TotalPaid = tp.Payments
                    .Where(p => !p.IsDeleted)
                    .Sum(p => p.Amount)
            })
            .ToListAsync(cancellationToken);

        var pendingPayments = travelPartiesWithFinancials
            .Sum(tp => Math.Max(0, tp.TotalPrice - tp.TotalPaid));

        return new DashboardStatsDto
        {
            TotalPublishedTrips = tripStats?.TotalPublished ?? 0,
            TotalDraftTrips = tripStats?.TotalDraft ?? 0,
            TotalPendingTravelers = travelerStats?.TotalPending ?? 0,
            TotalConfirmedTravelers = travelerStats?.TotalConfirmed ?? 0,
            TotalRequestedTravelers = travelerStats?.TotalRequested ?? 0,
            TotalRevenue = totalRevenue,
            PendingPayments = pendingPayments,
            TripsThisMonth = tripStats?.TripsThisMonth ?? 0,
            TravelersThisMonth = travelerStats?.TravelersThisMonth ?? 0
        };
    }
}
