using OdmoriBa.Application.Features.Dashboard.Models;
using OdmoriBa.Core.Domains.Travelers.Entities;

namespace OdmoriBa.Application.Features.Dashboard.Queries;

public sealed record GetPendingTravelersQuery(int Take = 10) : IQuery<Result<ListResultDto<PendingTravelerDto>>>;

internal sealed class GetPendingTravelersQueryHandler(IAppDbContext dbContext)
    : IQueryHandler<GetPendingTravelersQuery, Result<ListResultDto<PendingTravelerDto>>>
{
    public async ValueTask<Result<ListResultDto<PendingTravelerDto>>> Handle(GetPendingTravelersQuery request,
        CancellationToken cancellationToken)
    {
        var pendingTravelers = await dbContext.Travelers
            .Include(t => t.Person)
            .Include(t => t.TravelParty!)
            .ThenInclude(tp => tp.Trip)
            .Include(t => t.TravelParty!)
            .ThenInclude(tp => tp.TripDestination)
            .ThenInclude(td => td.Destination)
            .Where(t => !t.IsDeleted && 
                       (t.Status == TravelerStatus.Draft || t.Status == TravelerStatus.Requested))
            .OrderByDescending(t => t.CreatedAt)
            .Take(request.Take)
            .Select(t => new PendingTravelerDto
            {
                TravelerId = t.Id,
                TravelerName = $"{t.Person.FirstName} {t.Person.LastName}",
                TravelerEmail = t.Person.Email ?? "",
                Status = t.Status,
                TripId = t.TravelParty!.TripId,
                TripTitle = t.TravelParty.Trip!.Title,
                TripDestination = t.TravelParty.TripDestination.Destination!.Name,
                TripStartDate = t.TravelParty.Trip.StartDate,
                TripEndDate = t.TravelParty.Trip.EndDate,
                TransportationType = t.TravelParty.Trip.TransportationType,
                TotalPrice = t.Price + t.InsurancePrice + t.TaxPrice - t.Discount,
                RequestedAt = t.CreatedAt.DateTime,
                Note = t.Note
            })
            .ToListAsync(cancellationToken);

        return new ListResultDto<PendingTravelerDto>(pendingTravelers);
    }
}
