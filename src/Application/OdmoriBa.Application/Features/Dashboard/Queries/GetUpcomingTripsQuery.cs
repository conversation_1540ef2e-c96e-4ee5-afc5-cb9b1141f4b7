using OdmoriBa.Application.Features.Dashboard.Models;
using OdmoriBa.Core.Domains.Travelers.Entities;
using OdmoriBa.Core.Domains.Trips.Entities;

namespace OdmoriBa.Application.Features.Dashboard.Queries;

public sealed record GetUpcomingTripsQuery(int Take = 10) : IQuery<Result<ListResultDto<UpcomingTripDto>>>;

internal sealed class GetUpcomingTripsQueryHandler(IAppDbContext dbContext)
    : IQueryHandler<GetUpcomingTripsQuery, Result<ListResultDto<UpcomingTripDto>>>
{
    public async ValueTask<Result<ListResultDto<UpcomingTripDto>>> Handle(GetUpcomingTripsQuery request,
        CancellationToken cancellationToken)
    {
        var today = DateOnly.FromDateTime(DateTime.Now);
        
        var upcomingTrips = await dbContext.Trips
            .Include(t => t.TripDestinations)
            .ThenInclude(td => td.Destination)
            .Include(t => t.TravelParties)
            .ThenInclude(tp => tp.Travelers)
            .Include(t => t.TravelParties)
            .ThenInclude(tp => tp.Payments)
            .Where(t => t.Status == TripStatus.Published && t.StartDate >= today)
            .OrderBy(t => t.StartDate)
            .Take(request.Take)
            .Select(t => new UpcomingTripDto
            {
                TripId = t.Id,
                Title = t.Title,
                Destination = t.TripDestinations.FirstOrDefault()!.Destination!.Name,
                StartDate = t.StartDate,
                EndDate = t.EndDate,
                TransportationType = t.TransportationType,
                Status = t.Status,
                
                // Count reservations by status
                RequestedCount = t.TravelParties
                    .SelectMany(tp => tp.Travelers)
                    .Count(tr => !tr.IsDeleted && tr.Status == TravelerStatus.Requested),
                    
                DraftCount = t.TravelParties
                    .SelectMany(tp => tp.Travelers)
                    .Count(tr => !tr.IsDeleted && tr.Status == TravelerStatus.Draft),
                    
                ConfirmedCount = t.TravelParties
                    .SelectMany(tp => tp.Travelers)
                    .Count(tr => !tr.IsDeleted && tr.Status == TravelerStatus.Confirmed),
                    
                CancelledCount = t.TravelParties
                    .SelectMany(tp => tp.Travelers)
                    .Count(tr => !tr.IsDeleted && tr.Status == TravelerStatus.Cancelled),
                
                // Financial information
                TotalRevenue = t.TravelParties
                    .Where(tp => !tp.IsDeleted)
                    .SelectMany(tp => tp.Payments)
                    .Where(p => !p.IsDeleted)
                    .Sum(p => p.Amount),

                PendingPayments = t.TravelParties
                    .Where(tp => !tp.IsDeleted)
                    .Sum(tp => tp.Travelers
                        .Where(tr => !tr.IsDeleted)
                        .Sum(tr => tr.Price + tr.InsurancePrice + tr.TaxPrice - tr.Discount) -
                        tp.Payments
                        .Where(p => !p.IsDeleted)
                        .Sum(p => p.Amount))
            })
            .ToListAsync(cancellationToken);

        return new ListResultDto<UpcomingTripDto>(upcomingTrips);
    }
}
