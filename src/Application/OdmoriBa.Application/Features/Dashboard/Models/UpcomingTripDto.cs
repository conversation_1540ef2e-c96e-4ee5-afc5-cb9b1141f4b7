using OdmoriBa.Core.Domains.Trips.Entities;

namespace OdmoriBa.Application.Features.Dashboard.Models;

public class UpcomingTripDto
{
    public Guid TripId { get; set; }
    public string Title { get; set; } = null!;
    public string Destination { get; set; } = null!;
    public DateOnly StartDate { get; set; }
    public DateOnly EndDate { get; set; }
    public TransportationType TransportationType { get; set; }
    public TripStatus Status { get; set; }
    
    // Reservation counts by status
    public int RequestedCount { get; set; }
    public int DraftCount { get; set; }
    public int ConfirmedCount { get; set; }
    public int CancelledCount { get; set; }
    
    public int TotalReservations => RequestedCount + DraftCount + ConfirmedCount + CancelledCount;
    public int ActiveReservations => RequestedCount + DraftCount + ConfirmedCount;
    
    public string DateRange => $"{StartDate:dd.MM.} - {EndDate:dd.MM.}";
    
    // Revenue information
    public double TotalRevenue { get; set; }
    public double PendingPayments { get; set; }
}
