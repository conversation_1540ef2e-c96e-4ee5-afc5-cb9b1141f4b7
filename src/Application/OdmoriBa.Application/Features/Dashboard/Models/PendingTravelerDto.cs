using OdmoriBa.Core.Domains.Travelers.Entities;
using OdmoriBa.Core.Domains.Trips.Entities;

namespace OdmoriBa.Application.Features.Dashboard.Models;

public class PendingTravelerDto
{
    public Guid TravelerId { get; set; }
    public string TravelerName { get; set; } = null!;
    public string TravelerEmail { get; set; } = null!;
    public TravelerStatus Status { get; set; }
    public Guid TripId { get; set; }
    public string TripTitle { get; set; } = null!;
    public string TripDestination { get; set; } = null!;
    public DateOnly TripStartDate { get; set; }
    public DateOnly TripEndDate { get; set; }
    public TransportationType TransportationType { get; set; }
    public double TotalPrice { get; set; }
    public DateTime RequestedAt { get; set; }
    public string? Note { get; set; }
    
    public string DateRange => $"{TripStartDate:dd.MM.} - {TripEndDate:dd.MM.}";
}
